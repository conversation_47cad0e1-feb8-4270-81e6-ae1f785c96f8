export interface Pet {
  id: string;
  name: string;
  species: 'dog' | 'cat' | 'bird' | 'rabbit' | 'other';
  breed?: string;
  age: number;
  weight?: number;
  color?: string;
  microchipId?: string;
  medicalInfo: {
    allergies: string[];
    medications: string[];
    conditions: string[];
    lastVetVisit?: string;
    vaccinations: Vaccination[];
  };
  emergencyContact?: {
    vetClinic: string;
    phone: string;
    address: string;
  };
  photo?: string;
}

export interface Vaccination {
  name: string;
  date: string;
  nextDue?: string;
  veterinarian: string;
}
