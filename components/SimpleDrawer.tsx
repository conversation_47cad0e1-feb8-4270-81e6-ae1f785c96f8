import React from 'react';
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableWithoutFeedback,
  Animated,
} from 'react-native';
import { Pet } from '../types/Pet';
import { PetList } from './PetList';

const { width: SCREEN_WIDTH } = Dimensions.get('window');
const DRAWER_WIDTH = SCREEN_WIDTH * 0.8; // 80% of screen width

interface SimpleDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  pets: Pet[];
  selectedPet: Pet | null;
  onSelectPet: (pet: Pet) => void;
  onAddPet: () => void;
}

export const SimpleDrawer: React.FC<SimpleDrawerProps> = ({
  isOpen,
  onClose,
  pets,
  selectedPet,
  onSelectPet,
  onAddPet,
}) => {
  const translateX = React.useRef(new Animated.Value(-DRAWER_WIDTH)).current;

  React.useEffect(() => {
    Animated.spring(translateX, {
      toValue: isOpen ? 0 : -DRAWER_WIDTH,
      useNativeDriver: true,
      tension: 100,
      friction: 8,
    }).start();
  }, [isOpen, translateX]);

  if (!isOpen) {
    return null;
  }

  return (
    <View style={styles.container}>
      {/* Overlay */}
      <TouchableWithoutFeedback onPress={onClose}>
        <Animated.View style={styles.overlay} />
      </TouchableWithoutFeedback>

      {/* Drawer */}
      <Animated.View 
        style={[
          styles.drawer, 
          {
            transform: [{ translateX }],
          }
        ]}
      >
        <PetList
          pets={pets}
          selectedPet={selectedPet}
          onSelectPet={onSelectPet}
          onAddPet={onAddPet}
        />
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  drawer: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    width: DRAWER_WIDTH,
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOffset: {
      width: 2,
      height: 0,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
});
