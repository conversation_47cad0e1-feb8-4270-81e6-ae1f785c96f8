import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  TouchableWithoutFeedback,
  Animated,
  Platform,
} from 'react-native';
import { Pet } from '../types/Pet';
import { PetList } from './PetList';

const { width: SCREEN_WIDTH } = Dimensions.get('window');
const DRAWER_WIDTH = SCREEN_WIDTH * 0.8; // 80% of screen width

interface SimpleDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  pets: Pet[];
  selectedPet: Pet | null;
  onSelectPet: (pet: Pet) => void;
  onAddPet: () => void;
}

export const SimpleDrawer: React.FC<SimpleDrawerProps> = ({
  isOpen,
  onClose,
  pets,
  selectedPet,
  onSelectPet,
  onAddPet,
}) => {
  const translateX = React.useRef(new Animated.Value(-DRAWER_WIDTH)).current;

  React.useEffect(() => {
    Animated.timing(translateX, {
      toValue: isOpen ? 0 : -DRAWER_WIDTH,
      duration: isOpen ? 250 : 200, // Slightly faster close animation
      useNativeDriver: true,
    }).start();
  }, [isOpen, translateX]);

  if (!isOpen) {
    return null;
  }

  return (
    <View style={styles.container}>
      {/* Overlay */}
      <TouchableWithoutFeedback onPress={onClose}>
        <Animated.View style={styles.overlay} />
      </TouchableWithoutFeedback>

      {/* Drawer */}
      <Animated.View 
        style={[
          styles.drawer, 
          {
            transform: [{ translateX }],
          }
        ]}
      >
        <View style={styles.debugContainer}>
          <Text style={styles.debugText}>Platform: {Platform.OS}</Text>
        </View>
        <PetList
          pets={pets}
          selectedPet={selectedPet}
          onSelectPet={onSelectPet}
          onAddPet={onAddPet}
        />
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  drawer: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    width: DRAWER_WIDTH,
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOffset: {
      width: 2,
      height: 0,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  debugContainer: {
    backgroundColor: '#ff6b6b',
    padding: 8,
    alignItems: 'center',
  },
  debugText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
});
