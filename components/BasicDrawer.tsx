import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  TouchableWithoutFeedback,
  Platform,
} from 'react-native';
import { Pet } from '../types/Pet';
import { PetList } from './PetList';

const { width: SCREEN_WIDTH } = Dimensions.get('window');
const DRAWER_WIDTH = SCREEN_WIDTH * 0.8;

interface BasicDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  pets: Pet[];
  selectedPet: Pet | null;
  onSelectPet: (pet: Pet) => void;
  onAddPet: () => void;
}

export const BasicDrawer: React.FC<BasicDrawerProps> = ({
  isOpen,
  onClose,
  pets,
  selectedPet,
  onSelectPet,
  onAddPet,
}) => {
  console.log('BasicDrawer render - isOpen:', isOpen);

  if (!isOpen) {
    return null;
  }

  return (
    <View style={styles.container}>
      {/* Overlay */}
      <TouchableWithoutFeedback onPress={onClose}>
        <View style={styles.overlay} />
      </TouchableWithoutFeedback>

      {/* Drawer */}
      <View style={styles.drawer}>
        <View style={styles.debugContainer}>
          <Text style={styles.debugText}>Platform: {Platform.OS} (Basic Drawer - No Animations)</Text>
        </View>
        <PetList
          pets={pets}
          selectedPet={selectedPet}
          onSelectPet={onSelectPet}
          onAddPet={onAddPet}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  drawer: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    width: DRAWER_WIDTH,
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOffset: {
      width: 2,
      height: 0,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  debugContainer: {
    backgroundColor: '#ff9800',
    padding: 8,
    alignItems: 'center',
  },
  debugText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
    textAlign: 'center',
  },
});
