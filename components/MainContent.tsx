import React from 'react';
import { View, Text, ScrollView, StyleSheet, TouchableOpacity, Platform } from 'react-native';
import { Pet } from '../types/Pet';

interface MainContentProps {
  selectedPet: Pet | null;
  onOpenDrawer: () => void;
}

export const MainContent: React.FC<MainContentProps> = ({ selectedPet, onOpenDrawer }) => {
  const getSpeciesEmoji = (species: Pet['species']) => {
    switch (species) {
      case 'dog': return '🐕';
      case 'cat': return '🐱';
      case 'bird': return '🐦';
      case 'rabbit': return '🐰';
      default: return '🐾';
    }
  };

  if (!selectedPet) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity style={styles.menuButton} onPress={onOpenDrawer}>
            <Text style={styles.menuIcon}>☰</Text>
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Pet Medical Info</Text>
        </View>
        
        <View style={styles.emptyState}>
          <View style={styles.debugContainer}>
            <Text style={styles.debugText}>
              Platform: {Platform.OS} | Gestures: {Platform.OS !== 'web' ? 'Enabled' : 'Disabled'}
            </Text>
          </View>
          <Text style={styles.emptyIcon}>🐾</Text>
          <Text style={styles.emptyTitle}>No Pet Selected</Text>
          <Text style={styles.emptySubtitle}>
            {Platform.OS === 'web'
              ? 'Tap the menu to select a pet'
              : 'Swipe right or tap the menu to select a pet'
            }
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.menuButton} onPress={onOpenDrawer}>
          <Text style={styles.menuIcon}>☰</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{selectedPet.name}</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.petHeader}>
          <Text style={styles.petEmoji}>{getSpeciesEmoji(selectedPet.species)}</Text>
          <View style={styles.petInfo}>
            <Text style={styles.petName}>{selectedPet.name}</Text>
            <Text style={styles.petDetails}>
              {selectedPet.breed && `${selectedPet.breed} • `}
              {selectedPet.age} years old
              {selectedPet.weight && ` • ${selectedPet.weight}kg`}
            </Text>
            {selectedPet.color && (
              <Text style={styles.petColor}>Color: {selectedPet.color}</Text>
            )}
            {selectedPet.microchipId && (
              <Text style={styles.microchip}>Microchip: {selectedPet.microchipId}</Text>
            )}
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>🚨 Emergency Contact</Text>
          {selectedPet.emergencyContact ? (
            <View style={styles.emergencyCard}>
              <Text style={styles.clinicName}>{selectedPet.emergencyContact.vetClinic}</Text>
              <Text style={styles.clinicPhone}>{selectedPet.emergencyContact.phone}</Text>
              <Text style={styles.clinicAddress}>{selectedPet.emergencyContact.address}</Text>
            </View>
          ) : (
            <Text style={styles.noData}>No emergency contact set</Text>
          )}
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>💊 Medical Information</Text>
          
          {selectedPet.medicalInfo.allergies.length > 0 && (
            <View style={styles.medicalItem}>
              <Text style={styles.medicalLabel}>Allergies:</Text>
              <Text style={styles.medicalValue}>
                {selectedPet.medicalInfo.allergies.join(', ')}
              </Text>
            </View>
          )}

          {selectedPet.medicalInfo.medications.length > 0 && (
            <View style={styles.medicalItem}>
              <Text style={styles.medicalLabel}>Current Medications:</Text>
              <Text style={styles.medicalValue}>
                {selectedPet.medicalInfo.medications.join(', ')}
              </Text>
            </View>
          )}

          {selectedPet.medicalInfo.conditions.length > 0 && (
            <View style={styles.medicalItem}>
              <Text style={styles.medicalLabel}>Medical Conditions:</Text>
              <Text style={styles.medicalValue}>
                {selectedPet.medicalInfo.conditions.join(', ')}
              </Text>
            </View>
          )}

          {selectedPet.medicalInfo.lastVetVisit && (
            <View style={styles.medicalItem}>
              <Text style={styles.medicalLabel}>Last Vet Visit:</Text>
              <Text style={styles.medicalValue}>{selectedPet.medicalInfo.lastVetVisit}</Text>
            </View>
          )}
        </View>

        {selectedPet.medicalInfo.vaccinations.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>💉 Vaccinations</Text>
            {selectedPet.medicalInfo.vaccinations.map((vaccination, index) => (
              <View key={index} style={styles.vaccinationCard}>
                <Text style={styles.vaccinationName}>{vaccination.name}</Text>
                <Text style={styles.vaccinationDate}>Given: {vaccination.date}</Text>
                {vaccination.nextDue && (
                  <Text style={styles.vaccinationNext}>Next due: {vaccination.nextDue}</Text>
                )}
                <Text style={styles.vaccinationVet}>By: {vaccination.veterinarian}</Text>
              </View>
            ))}
          </View>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    paddingTop: 50,
    paddingBottom: 16,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  menuButton: {
    padding: 8,
    marginRight: 12,
  },
  menuIcon: {
    fontSize: 20,
    color: '#333',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333',
  },
  content: {
    flex: 1,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyIcon: {
    fontSize: 64,
    marginBottom: 16,
  },
  emptyTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
  },
  petHeader: {
    backgroundColor: '#fff',
    padding: 20,
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  petEmoji: {
    fontSize: 48,
    marginRight: 16,
  },
  petInfo: {
    flex: 1,
  },
  petName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  petDetails: {
    fontSize: 16,
    color: '#666',
    marginBottom: 4,
  },
  petColor: {
    fontSize: 14,
    color: '#888',
    marginBottom: 2,
  },
  microchip: {
    fontSize: 12,
    color: '#999',
    fontFamily: 'monospace',
  },
  section: {
    backgroundColor: '#fff',
    marginBottom: 16,
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
  },
  emergencyCard: {
    backgroundColor: '#fff3cd',
    padding: 16,
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#ffc107',
  },
  clinicName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  clinicPhone: {
    fontSize: 16,
    color: '#007bff',
    marginBottom: 4,
    fontWeight: '500',
  },
  clinicAddress: {
    fontSize: 14,
    color: '#666',
  },
  medicalItem: {
    marginBottom: 12,
  },
  medicalLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  medicalValue: {
    fontSize: 14,
    color: '#666',
  },
  noData: {
    fontSize: 14,
    color: '#999',
    fontStyle: 'italic',
  },
  vaccinationCard: {
    backgroundColor: '#f8f9fa',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  vaccinationName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  vaccinationDate: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  vaccinationNext: {
    fontSize: 14,
    color: '#28a745',
    marginBottom: 2,
  },
  vaccinationVet: {
    fontSize: 12,
    color: '#999',
  },
  debugContainer: {
    backgroundColor: '#2196f3',
    padding: 8,
    borderRadius: 4,
    marginBottom: 16,
    alignItems: 'center',
  },
  debugText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
    textAlign: 'center',
  },
});
