import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Pet } from '../types/Pet';

interface PetCardProps {
  pet: Pet;
  isSelected: boolean;
  onSelect: (pet: Pet) => void;
}

export const PetCard: React.FC<PetCardProps> = ({ pet, isSelected, onSelect }) => {
  const getSpeciesEmoji = (species: Pet['species']) => {
    switch (species) {
      case 'dog': return '🐕';
      case 'cat': return '🐱';
      case 'bird': return '🐦';
      case 'rabbit': return '🐰';
      default: return '🐾';
    }
  };

  return (
    <TouchableOpacity
      style={[styles.container, isSelected && styles.selected]}
      onPress={() => onSelect(pet)}
      activeOpacity={0.7}
    >
      <View style={styles.content}>
        <Text style={styles.emoji}>{getSpeciesEmoji(pet.species)}</Text>
        <View style={styles.info}>
          <Text style={[styles.name, isSelected && styles.selectedText]}>
            {pet.name}
          </Text>
          <Text style={[styles.details, isSelected && styles.selectedText]}>
            {pet.breed ? `${pet.breed} • ` : ''}{pet.age} years old
          </Text>
        </View>
      </View>
      {isSelected && <View style={styles.selectedIndicator} />}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#f8f9fa',
    marginHorizontal: 16,
    marginVertical: 4,
    borderRadius: 12,
    padding: 16,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selected: {
    backgroundColor: '#e3f2fd',
    borderColor: '#2196f3',
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  emoji: {
    fontSize: 32,
    marginRight: 12,
  },
  info: {
    flex: 1,
  },
  name: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  details: {
    fontSize: 14,
    color: '#666',
  },
  selectedText: {
    color: '#1976d2',
  },
  selectedIndicator: {
    position: 'absolute',
    right: 16,
    top: '50%',
    marginTop: -6,
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#2196f3',
  },
});
