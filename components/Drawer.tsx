import React from 'react';
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableWithoutFeedback,
} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  runOnJS,
} from 'react-native-reanimated';
import {
  PanGestureHandler,
  GestureHandlerRootView,
  PanGestureHandlerGestureEvent,
} from 'react-native-gesture-handler';
import { Pet } from '../types/Pet';
import { PetList } from './PetList';

const { width: SCREEN_WIDTH } = Dimensions.get('window');
const DRAWER_WIDTH = SCREEN_WIDTH * 0.8; // 80% of screen width

interface DrawerProps {
  isOpen: boolean;
  onClose: () => void;
  pets: Pet[];
  selectedPet: Pet | null;
  onSelectPet: (pet: Pet) => void;
  onAddPet: () => void;
}

export const Drawer: React.FC<DrawerProps> = ({
  isOpen,
  onClose,
  pets,
  selectedPet,
  onSelectPet,
  onAddPet,
}) => {
  const translateX = useSharedValue(-DRAWER_WIDTH);

  React.useEffect(() => {
    translateX.value = withSpring(isOpen ? 0 : -DRAWER_WIDTH, {
      damping: 20,
      stiffness: 90,
    });
  }, [isOpen, translateX]);

  const onGestureEvent = (event: PanGestureHandlerGestureEvent) => {
    const { translationX } = event.nativeEvent;
    
    // Only allow dragging to the left (closing)
    if (translationX <= 0) {
      translateX.value = Math.max(translationX, -DRAWER_WIDTH);
    }
  };

  const onGestureEnd = (event: PanGestureHandlerGestureEvent) => {
    const { translationX, velocityX } = event.nativeEvent;
    
    // Close drawer if dragged more than 50% or with sufficient velocity
    const shouldClose = translationX < -DRAWER_WIDTH * 0.5 || velocityX < -500;
    
    if (shouldClose) {
      translateX.value = withSpring(-DRAWER_WIDTH, {
        damping: 20,
        stiffness: 90,
      });
      runOnJS(onClose)();
    } else {
      translateX.value = withSpring(0, {
        damping: 20,
        stiffness: 90,
      });
    }
  };

  const drawerAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateX: translateX.value }],
    };
  });

  const overlayAnimatedStyle = useAnimatedStyle(() => {
    const opacity = (translateX.value + DRAWER_WIDTH) / DRAWER_WIDTH * 0.5;
    return {
      opacity: Math.max(0, opacity),
    };
  });

  if (!isOpen && translateX.value <= -DRAWER_WIDTH) {
    return null;
  }

  return (
    <GestureHandlerRootView style={StyleSheet.absoluteFillObject}>
      <View style={styles.container}>
        {/* Overlay */}
        <TouchableWithoutFeedback onPress={onClose}>
          <Animated.View style={[styles.overlay, overlayAnimatedStyle]} />
        </TouchableWithoutFeedback>

        {/* Drawer */}
        <PanGestureHandler
          onGestureEvent={onGestureEvent}
          onEnded={onGestureEnd}
          activeOffsetX={[-10, 10]}
        >
          <Animated.View style={[styles.drawer, drawerAnimatedStyle]}>
            <PetList
              pets={pets}
              selectedPet={selectedPet}
              onSelectPet={onSelectPet}
              onAddPet={onAddPet}
            />
          </Animated.View>
        </PanGestureHandler>
      </View>
    </GestureHandlerRootView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: '#000',
  },
  drawer: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    width: DRAWER_WIDTH,
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOffset: {
      width: 2,
      height: 0,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
});
