import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  TouchableWithoutFeedback,
  Platform,
} from 'react-native';
import { Pet } from '../types/Pet';
import { PetList } from './PetList';

// Import reanimated and gesture handler with error handling
let Animated: any;
let useSharedValue: any;
let useAnimatedStyle: any;
let withSpring: any;
let runOnJS: any;
let PanGestureHandler: any;
let GestureHandlerRootView: any;

try {
  const reanimated = require('react-native-reanimated');
  Animated = reanimated.default;
  useSharedValue = reanimated.useSharedValue;
  useAnimatedStyle = reanimated.useAnimatedStyle;
  withSpring = reanimated.withSpring;
  runOnJS = reanimated.runOnJS;

  const gestureHandler = require('react-native-gesture-handler');
  PanGestureHandler = gestureHandler.PanGestureHandler;
  GestureHandlerRootView = gestureHandler.GestureHandlerRootView;
} catch (error) {
  // Fallback to SimpleDrawer if libraries are not available
  console.warn('Reanimated or GestureHandler not available, falling back to SimpleDrawer');
}

const { width: SCREEN_WIDTH } = Dimensions.get('window');
const DRAWER_WIDTH = SCREEN_WIDTH * 0.8; // 80% of screen width

interface DrawerProps {
  isOpen: boolean;
  onClose: () => void;
  pets: Pet[];
  selectedPet: Pet | null;
  onSelectPet: (pet: Pet) => void;
  onAddPet: () => void;
}

export const Drawer: React.FC<DrawerProps> = ({
  isOpen,
  onClose,
  pets,
  selectedPet,
  onSelectPet,
  onAddPet,
}) => {
  // Fallback to SimpleDrawer if reanimated is not available
  if (!useSharedValue || !Animated) {
    const { SimpleDrawer } = require('./SimpleDrawer');
    return (
      <SimpleDrawer
        isOpen={isOpen}
        onClose={onClose}
        pets={pets}
        selectedPet={selectedPet}
        onSelectPet={onSelectPet}
        onAddPet={onAddPet}
      />
    );
  }

  const translateX = useSharedValue(-DRAWER_WIDTH);

  React.useEffect(() => {
    translateX.value = withSpring(isOpen ? 0 : -DRAWER_WIDTH, {
      damping: 20,
      stiffness: 90,
    });
  }, [isOpen, translateX]);

  const onGestureEvent = (event: PanGestureHandlerGestureEvent) => {
    const { translationX } = event.nativeEvent;
    
    // Only allow dragging to the left (closing)
    if (translationX <= 0) {
      translateX.value = Math.max(translationX, -DRAWER_WIDTH);
    }
  };

  const onGestureEnd = (event: PanGestureHandlerGestureEvent) => {
    const { translationX, velocityX } = event.nativeEvent;
    
    // Close drawer if dragged more than 50% or with sufficient velocity
    const shouldClose = translationX < -DRAWER_WIDTH * 0.5 || velocityX < -500;
    
    if (shouldClose) {
      translateX.value = withSpring(-DRAWER_WIDTH, {
        damping: 20,
        stiffness: 90,
      });
      runOnJS(onClose)();
    } else {
      translateX.value = withSpring(0, {
        damping: 20,
        stiffness: 90,
      });
    }
  };

  const drawerAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateX: translateX.value }],
    };
  });

  const overlayAnimatedStyle = useAnimatedStyle(() => {
    const opacity = (translateX.value + DRAWER_WIDTH) / DRAWER_WIDTH * 0.5;
    return {
      opacity: Math.max(0, opacity),
    };
  });

  if (!isOpen && translateX.value <= -DRAWER_WIDTH) {
    return null;
  }

  return (
    <GestureHandlerRootView style={StyleSheet.absoluteFillObject}>
      <View style={styles.container}>
        {/* Overlay */}
        <TouchableWithoutFeedback onPress={onClose}>
          <Animated.View style={[styles.overlay, overlayAnimatedStyle]} />
        </TouchableWithoutFeedback>

        {/* Drawer */}
        <PanGestureHandler
          onGestureEvent={onGestureEvent}
          onEnded={onGestureEnd}
          activeOffsetX={[-10, 10]}
        >
          <Animated.View style={[styles.drawer, drawerAnimatedStyle]}>
            <View style={styles.debugContainer}>
              <Text style={styles.debugText}>Platform: {Platform.OS} (Advanced Drawer)</Text>
            </View>
            <PetList
              pets={pets}
              selectedPet={selectedPet}
              onSelectPet={onSelectPet}
              onAddPet={onAddPet}
            />
          </Animated.View>
        </PanGestureHandler>
      </View>
    </GestureHandlerRootView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: '#000',
  },
  drawer: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    width: DRAWER_WIDTH,
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOffset: {
      width: 2,
      height: 0,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  debugContainer: {
    backgroundColor: '#4caf50',
    padding: 8,
    alignItems: 'center',
  },
  debugText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
});
