import { Pet } from '../types/Pet';

export const mockPets: <PERSON>[] = [
  {
    id: '1',
    name: '<PERSON>',
    species: 'dog',
    breed: 'Golden Retriever',
    age: 5,
    weight: 30,
    color: 'Golden',
    microchipId: 'GR123456789',
    medicalInfo: {
      allergies: ['Chicken', 'Wheat'],
      medications: ['Heartgard Plus'],
      conditions: ['Hip Dysplasia'],
      lastVetVisit: '2024-05-15',
      vaccinations: [
        {
          name: 'Rabies',
          date: '2024-01-15',
          nextDue: '2025-01-15',
          veterinarian: 'Dr. <PERSON>'
        },
        {
          name: 'DHP<PERSON>',
          date: '2024-01-15',
          nextDue: '2025-01-15',
          veterinarian: 'Dr. <PERSON>'
        }
      ]
    },
    emergencyContact: {
      vetClinic: 'City Animal Hospital',
      phone: '******-0123',
      address: '123 Main St, City, State 12345'
    }
  },
  {
    id: '2',
    name: 'Whiskers',
    species: 'cat',
    breed: 'Persian',
    age: 3,
    weight: 4.5,
    color: 'White',
    microchipId: 'CAT987654321',
    medicalInfo: {
      allergies: ['Fish'],
      medications: ['Flea prevention'],
      conditions: [],
      lastVetVisit: '2024-04-20',
      vaccinations: [
        {
          name: 'FVRCP',
          date: '2024-02-10',
          nextDue: '2025-02-10',
          veterinarian: 'Dr. Johnson'
        }
      ]
    },
    emergencyContact: {
      vetClinic: 'Pet Care Center',
      phone: '******-0456',
      address: '456 Oak Ave, City, State 12345'
    }
  },
  {
    id: '3',
    name: 'Charlie',
    species: 'bird',
    breed: 'Cockatiel',
    age: 2,
    weight: 0.1,
    color: 'Gray and Yellow',
    medicalInfo: {
      allergies: [],
      medications: [],
      conditions: [],
      lastVetVisit: '2024-03-10',
      vaccinations: []
    },
    emergencyContact: {
      vetClinic: 'Exotic Pet Clinic',
      phone: '******-0789',
      address: '789 Bird Lane, City, State 12345'
    }
  }
];
