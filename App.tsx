import React, { useState } from 'react';
import { StatusBar } from 'expo-status-bar';
import { StyleSheet, View, Alert, Platform } from 'react-native';
import { Pet } from './types/Pet';
import { mockPets } from './data/mockPets';
import { MainContent } from './components/MainContent';
import { SimpleDrawer } from './components/SimpleDrawer';

// Import gesture components conditionally
let GestureHandlerRootView: any;
let PanGestureHandler: any;
let Drawer: any;

if (Platform.OS !== 'web') {
  try {
    const gestureHandler = require('react-native-gesture-handler');
    GestureHandlerRootView = gestureHandler.GestureHandlerRootView;
    PanGestureHandler = gestureHandler.PanGestureHandler;
    Drawer = require('./components/Drawer').Drawer;
  } catch (error) {
    // Fallback to simple components if gesture handler is not available
    GestureHandlerRootView = View;
    PanGestureHandler = View;
    Drawer = SimpleDrawer;
  }
} else {
  GestureHandlerRootView = View;
  PanGestureHandler = View;
  Drawer = SimpleDrawer;
}

export default function App() {
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [pets] = useState<Pet[]>(mockPets);
  const [selectedPet, setSelectedPet] = useState<Pet | null>(pets[0] || null);

  const openDrawer = () => {
    setIsDrawerOpen(true);
  };

  const closeDrawer = () => {
    setIsDrawerOpen(false);
  };

  const handleSelectPet = (pet: Pet) => {
    setSelectedPet(pet);
    closeDrawer();
  };

  const handleAddPet = () => {
    Alert.alert(
      'Add New Pet',
      'This feature will be implemented in the next version.',
      [{ text: 'OK' }]
    );
  };

  const onSwipeGesture = Platform.OS !== 'web' ? (event: any) => {
    const { translationX, velocityX } = event.nativeEvent;

    // Open drawer on swipe right from left edge (more sensitive for easier opening)
    if (translationX > 30 && velocityX > 100 && !isDrawerOpen) {
      try {
        const { runOnJS } = require('react-native-reanimated');
        runOnJS(openDrawer)();
      } catch (error) {
        // Fallback if reanimated is not available
        setTimeout(openDrawer, 0);
      }
    }
  } : undefined;

  // For web, use simple layout
  if (Platform.OS === 'web') {
    return (
      <View style={styles.container}>
        <MainContent selectedPet={selectedPet} onOpenDrawer={openDrawer} />
        <StatusBar style="auto" />
        <SimpleDrawer
          isOpen={isDrawerOpen}
          onClose={closeDrawer}
          pets={pets}
          selectedPet={selectedPet}
          onSelectPet={handleSelectPet}
          onAddPet={handleAddPet}
        />
      </View>
    );
  }

  // For mobile, use gesture handler
  return (
    <GestureHandlerRootView style={styles.container}>
      <PanGestureHandler
        onGestureEvent={onSwipeGesture}
        activeOffsetX={[-5, 1000]}
        failOffsetY={[-100, 100]}
        minPointers={1}
        maxPointers={1}
      >
        <View style={styles.container}>
          <MainContent selectedPet={selectedPet} onOpenDrawer={openDrawer} />
          <StatusBar style="auto" />
        </View>
      </PanGestureHandler>

      <Drawer
        isOpen={isDrawerOpen}
        onClose={closeDrawer}
        pets={pets}
        selectedPet={selectedPet}
        onSelectPet={handleSelectPet}
        onAddPet={handleAddPet}
      />
    </GestureHandlerRootView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
});
